<div class="card">
  <label class="block text-sm font-medium text-gray-700 mb-2">
    <i class="fas fa-upload mr-2"></i>
    {{ title }}
  </label>
  
  <div 
    class="file-upload-area"
    [class.dragover]="isDragOver"
    (dragover)="onDragOver($event)"
    (dragleave)="onDragLeave($event)"
    (drop)="onDrop($event)"
    (click)="fileInput.click()">
    
    <input
      #fileInput
      type="file"
      [accept]="acceptedTypes"
      [multiple]="allowMultiple"
      [attr.webkitdirectory]="allowDirectory ? '' : null"
      (change)="onFileSelected($event)"
      class="hidden">
    
    <div *ngIf="!uploadedFileName && uploadedFiles.length === 0" class="text-center">
      <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
      <p class="text-sm text-gray-600 mb-2">{{ description }}</p>
      <p class="text-xs text-gray-500">
        <span *ngIf="allowMultiple">Drop multiple files here or click to browse</span>
        <span *ngIf="!allowMultiple">Drop files here or click to browse</span>
        <span *ngIf="allowDirectory" class="block mt-1">
          <i class="fas fa-folder mr-1"></i>
          Supports directory upload for bulk PDF processing
        </span>
      </p>
    </div>

    <div *ngIf="uploadedFileName && !allowMultiple" class="text-center">
      <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
      <p class="text-sm text-green-600 font-medium">{{ uploadedFileName }}</p>
      <p class="text-xs text-gray-500 mt-1">
        Click to upload a different file
      </p>
    </div>

    <div *ngIf="uploadedFiles.length > 0 && allowMultiple" class="text-center">
      <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
      <p class="text-sm text-green-600 font-medium">
        {{ uploadedFiles.length }} files selected
      </p>
      <div class="text-xs text-gray-500 mt-2 max-h-20 overflow-y-auto">
        <div *ngFor="let file of uploadedFiles.slice(0, 5)" class="truncate">
          {{ file.name }}
        </div>
        <div *ngIf="uploadedFiles.length > 5" class="text-gray-400">
          ... and {{ uploadedFiles.length - 5 }} more files
        </div>
      </div>
      <p class="text-xs text-gray-500 mt-2">
        Click to upload different files
      </p>
    </div>
  </div>
  
  <div *ngIf="error" class="mt-2 text-red-600 text-sm">
    <i class="fas fa-exclamation-triangle mr-1"></i>
    {{ error }}
  </div>
  
  <div *ngIf="uploading" class="mt-2 flex items-center text-blue-600 text-sm">
    <div class="loading-spinner mr-2"></div>
    Uploading...
  </div>
</div>
