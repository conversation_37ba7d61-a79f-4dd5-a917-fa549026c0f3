# Embedding Benchmark Tool

A tool benchmarking embedding models. Compare different AI models, and get comprehensive performance metrics on how well embeddings retrieve relevant items for a given query.

## Table of Contents

- [🌟 Overview](#-overview)
- [⚡ Quick Start](#-quick-start)
- [🛠️ Technology Stack](#️-technology-stack)
- [📦 Installation](#-installation)
- [🎯 How to Use](#-how-to-use)
- [📊 Data Formats](#-data-formats)
- [🏗️ Architecture](#️-architecture)
- [🔧 Configuration](#-configuration)

## 🌟 Overview

This tool helps you:
- **Test different AI embedding models** to find the best one for your use case
- **Upload your own queries** and see how well models retrieve relevant documents
- **Get detailed performance metrics** including precision, recall, and advanced rankings
- **Compare models side-by-side** with comprehensive benchmarking

### ✨ Key Features

- 📁 **Flexible File Upload**: Support for PDF, TXT, and JSON formats
- 📊 **Comprehensive Metrics**: Precision, Recall, F1, nDCG, MRR, Embedding Times
- ⚡ **Real-time Progress**: Live updates during benchmark execution
- 📥 **Export Results**: Download detailed JSON reports
- 🎯 **Streamlined Workflow**: Upload one file with both queries and ground truth

## ⚡ Quick Start

**Want to try it right now?** Follow these 3 simple steps:

1. **Start the backend**: `./mvnw spring-boot:run`
2. **Start the frontend**: `cd embedding-benchmark-ui && npm start`
3. **Open your browser**: Go to `http://localhost:4200`


## 🛠️ Technology Stack

### Backend Technologies
| Technology | Version | Purpose |
|------------|---------|---------|
| **Java** | 17+ | Core programming language |
| **Spring Boot** | 3.x | REST API framework |
| **Maven** | 3.6+ | Dependency management |
| **Elasticsearch** | 8.x | Vector search engine |
| **Apache PDFBox** | 3.x | PDF text extraction |
| **Jackson** | Latest | JSON processing |

### Frontend Technologies
| Technology | Version | Purpose |
|------------|---------|---------|
| **Angular** | 17+ | Web application framework |
| **TypeScript** | 5.x | Type-safe JavaScript |
| **Tailwind CSS** | 3.x | Utility-first CSS framework |
| **RxJS** | 7.x | Reactive programming |
| **Font Awesome** | 6.x | Icons and UI elements |

### AI/ML Technologies
| Technology | Version | Purpose |
|------------|---------|---------|
| **Ollama** | Latest | Local LLM and embedding models |


### Development Tools
| Tool | Version | Purpose |
|------|---------|---------|
| **Node.js** | 18+ | JavaScript runtime |
| **npm** | 9+ | Package manager |
| **Git** | Latest | Version control |

##  Installation

### System Requirements


| Requirement | Minimum Version | Recommended | Notes |
|-------------|----------------|-------------|-------|
| **Java** | 17 | 21 | Required for backend |
| **Node.js** | 18 | 20+ | Required for frontend |


### Step-by-Step Installation


#### 1. 🚀 Start the Application

**Clone and Start Backend**
```bash
# Clone the repository
git clone <your-repo-url>
cd pdfimporter

# Start the backend (this may take a few minutes first time)
./mvnw spring-boot:run
```

**Start Frontend (in a new terminal)**
```bash
# Navigate to frontend directory
cd embedding-benchmark-ui

# Install dependencies (first time only)
npm install

# Start the development server
npm start
```

#### 3. ✅ Verify Installation

1. **Backend**: Open `http://localhost:8080/api/benchmark/models` - should show available models
2. **Frontend**: Open `http://localhost:4200` - should show the application interface
3. **Elasticsearch**: Open `http://localhost:9200` - should show cluster info (if installed)


##  How to Use

### Step 1: Choose Your Embedding Model 🤖

1. **Open the application** at `http://localhost:4200`
2. **Select a model** from the dropdown:
   - **Ollama Jina**: Multilingual, great for German/English content

💡 **Tip**: Models are automatically tested for availability. If a model shows as unavailable, check if the service is running.

### Step 2: Upload Your Documents 📁


**Multiple Files (Recommended for large datasets)**
- **Drag and drop** multiple PDF files
- **Select entire directories** containing documents
- **Mix different formats** in one upload
- Files are processed automatically and combined

**Example**: Upload a folder with 100 research papers and they'll all be processed into one searchable dataset.

### Step 3: Upload Queries & Ground Truth 📝

### 📝 Queries & Ground Truth Format

**The Simple Way** (Recommended for most users):

```json
{
  "What nutrients does the body need?": [
    "Core Human Nutrients",
    "Government Dietary Guidelines",
    "Plant Diet Overview",
    "Dietary Tech Tools",
    "Spaceflight Nutrient Planning"
  ],
  "What was the Apollo program about?": [
    "Apollo Program Guide",
    "Legacy of Apollo",
    "Lunar Missions Summary",
    "Artemis Program",
    "Plant Diet Overview"
  ],
  "What do experts recommend for a healthy diet?": [
    "Government Dietary Guidelines",
    "Core Human Nutrients",
    "Plant Diet Overview",
    "Dietary Tech Tools",
    "Spaceflight Nutrient Planning"
  ],
  "How is technology used in space exploration?": [
    "AI in Space Exploration",
    "Legacy of Apollo",
    "Artemis Program",
    "Government Dietary Guidelines"
  ],
  "Which missions aim to return humans to the Moon?": [
    "Artemis Program",
    "Legacy of Apollo",
    "Lunar Missions Summary",
    "Apollo Program Guide",
    "AI in Space Exploration"
  ]
}

```

### 🔍 Document ID Matching

**How the system matches documents:**

1. **Removes file extensions**: `"report.pdf"` → `"report"`
2. **Removes special characters**: `"AI_Research-Paper (2023).pdf"` → `"airesearchpaper2023"`
3. **Converts to lowercase**: `"Core Human Nutrients"` → `"corehumannutrients"`

**Pro Tip**: Use descriptive filenames and document titles for better matching!

💡 **No separate files needed!** The system automatically extracts queries from the keys and ground truth from the values.

### Step 4: Configure & Run 🚀

1. **Set similarity function**:
   - **Cosine**: Best for most use cases
   - **Dot Product**: Good for normalized vectors
   - **L2 Norm**: Euclidean distance

2. **Set Top K results**: How many documents to retrieve (1-100)

3. **Click "Start Benchmark"** and watch the magic happen!

### Step 5: Analyze Results 📊

**Real-time Progress**: See live updates as your benchmark runs

**Comprehensive Metrics**:

| Metric | What It Measures | Good Score | Example |
|--------|------------------|------------|---------|
| **Precision** | How many retrieved docs were relevant? | 70%+ | If you get 10 results and 7 are relevant = 70% |
| **Recall** | How many relevant docs were found? | 80%+ | If there are 10 relevant docs and you found 8 = 80% |
| **F1 Score** | Balance of precision and recall | 75%+ | Harmonic mean of precision and recall |
| **nDCG** | Quality of ranking (order matters) | 0.8+ | Higher score = relevant docs appear first (considers document order in ground truth) |
| **MRR** | How quickly you find relevant results | 0.7+ | Higher score = relevant docs at top |
| **Avg Doc Embed** | Time to generate document embeddings | <500ms | Lower is faster for indexing |
| **Avg Query Embed** | Time to generate query embeddings | <200ms | Lower is faster for searching |
**Export Results**: Download detailed JSON reports for further analysis


## 🏗️ Architecture

### System Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   AI Services   │
│   (Angular)     │◄──►│  (Spring Boot)  │◄──►│    (Ollama)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │  Elasticsearch  │    │  Embedding      │
│   (User Interface)   │  (Vector Store)  │    │  Models         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Backend Components

| Component | Purpose | Key Features |
|-----------|---------|--------------|
| **BenchmarkService** | Orchestrates benchmark execution | Multi-threaded processing, progress tracking |
| **EmbeddingModelManager** | Manages AI model connections | Auto-discovery, health checks, failover |
| **FileProcessingService** | Handles file uploads | PDF extraction, format conversion, validation |
| **ElasticService** | Vector search operations | Indexing, similarity search, aggregations |
| **ChromaService** | Alternative vector store | High-performance embeddings, metadata filtering |

### Frontend Components

| Component | Purpose | Key Features |
|-----------|---------|--------------|
| **Model Selector** | Choose embedding models | Real-time availability, performance hints |
| **File Uploader** | Upload documents and queries | Drag-drop, progress bars, validation |
| **Benchmark Runner** | Configure and start tests | Parameter tuning, real-time monitoring |
| **Results Display** | Show metrics and reports | Interactive charts, export options |

### Data Flow

1. **Upload**: User uploads documents and queries through Angular frontend
2. **Processing**: Spring Boot backend extracts text and processes files
3. **Indexing**: Documents are converted to embeddings and stored in vector database
4. **Querying**: Queries are converted to embeddings and searched against document vectors
5. **Evaluation**: Results are compared against ground truth and metrics calculated
6. **Display**: Results are sent back to frontend for visualization

## 🔧 Configuration

### Backend Configuration

**Basic Settings** (`application.properties`):
```properties
# Server settings
server.port=8080

# Benchmark settings
benchmark.default-top-k=10
benchmark.max-top-k=100

# File upload limits
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=500MB

# Elasticsearch settings
elasticsearch.host=localhost
elasticsearch.port=9200

# Ollama settings
ollama.base-url=http://localhost:11434
```



### 📊 Real-World Examples

**Scenario 1: Medical Research**
- Query: "What are the side effects of aspirin?"
- Good result: Medical papers about aspirin appear first
- Bad result: General chemistry papers appear first

**Scenario 2: Legal Documents**
- Query: "Contract termination clauses"
- Good result: Contract law documents with termination sections
- Bad result: Random legal documents without termination info

### 🎯 Interpreting Your Results

**Excellent Performance (90%+ metrics)**:
- Your model is very well-suited for your data
- Ground truth is high quality
- Document collection is relevant

**Good Performance (70-90% metrics)**:
- Model works well, minor improvements possible
- Consider fine-tuning or different similarity functions

**Poor Performance (<50% metrics)**:
- Check for document ID mismatches
- Verify ground truth quality
- Try different embedding models

### 🚀 Embedding Performance Analysis

**Understanding Embedding Times**:

| Embedding Time | Performance Level | Use Case |
|----------------|------------------|----------|
| **<100ms per doc** | Excellent | Real-time applications, large datasets |
| **100-500ms per doc** | Good | Batch processing, medium datasets |
| **500ms-1s per doc** | Moderate | Small datasets, complex models |
| **>1s per doc** | Slow | Research, very complex models |

**Performance Factors**:
- **Model Size**: Larger models (like large transformers) take longer
- **Text Length**: Longer documents require more processing time
- **Hardware**: GPU acceleration significantly improves speed
- **Model Provider**: Local models (Ollama) vs. cloud APIs have different characteristics

**Optimization Tips**:
- Use **MiniLM models** for speed-critical applications
- Consider **batch processing** for large datasets
- Enable **GPU acceleration** when available
- **Cache embeddings** for repeated use




### 📊 Response Examples

**Model List Response**:
```json
[
  {
    "id": "elasticsearch-minilm",
    "name": "Elasticsearch MiniLM",
    "provider": "elasticsearch",
    "available": true,
    "modelName": "sentence-transformers__all-minilm-l6-v2"
  }
]
```

**Benchmark Results**:
```json
{
  "jobId": "bench-123",
  "status": "COMPLETED",
  "overallMetrics": {
    "avgPrecision": 0.85,
    "avgRecall": 0.78,
    "avgF1": 0.81,
    "avgNdcg": 0.89,
    "avgMrr": 0.72
  }
}
```