package os.tukan.extractor.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import os.tukan.extractor.config.EmbeddingModelConfig;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BenchmarkResult {

    private String jobId;
    private String sessionId;
    private BenchmarkStatus status;
    private BenchmarkMetrics overallMetrics;
    private List<QueryBenchmarkResult> queryResults;
    private long executionTimeMs;
    private EmbeddingModelConfig modelInfo;
    private String errorMessage;
    private String label;
    private String createdAt;

    public BenchmarkResult(String jobId, BenchmarkStatus status) {
        this.jobId = jobId;
        this.status = status;
    }

    public BenchmarkResult(String jobId, BenchmarkStatus status, String errorMessage) {
        this.jobId = jobId;
        this.status = status;
        this.errorMessage = errorMessage;
    }
}
