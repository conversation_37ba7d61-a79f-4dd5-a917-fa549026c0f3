import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BenchmarkApiService } from './services/benchmark-api.service';
import {
  BenchmarkConfig,
  BenchmarkResult,
  BenchmarkStatus,
  UploadResponse
} from './models/benchmark.models';
import { ModelSelectorComponent } from './components/model-selector/model-selector.component';
import { FileUploaderComponent } from './components/file-uploader/file-uploader.component';
import { BenchmarkRunnerComponent } from './components/benchmark-runner/benchmark-runner.component';
import { ResultsDisplayComponent } from './components/results-display/results-display.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ModelSelectorComponent,
    FileUploaderComponent,
    BenchmarkRunnerComponent,
    ResultsDisplayComponent
  ],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  config: BenchmarkConfig = {
    modelId: '',
    datasetId: '',
    queries: [],
    groundTruth: {},
    similarityFunction: 'cosine',
    topK: 5
  };

  benchmarkResult: BenchmarkResult | null = null;
  isRunning = false;
  currentJobId: string | null = null;

  datasetFileName = '';
  groundTruthFileName = '';

  datasetAcceptedTypes = '.pdf,.txt,.json';
  groundTruthAcceptedTypes = '.json';

  private pollInterval: any;

  constructor(private benchmarkApi: BenchmarkApiService) { }

  ngOnInit() {
    // Initialize component
  }

  ngOnDestroy() {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
    }
  }

  onModelChange(modelId: string) {
    this.config.modelId = modelId;
  }

  onDatasetUpload(file: File) {
    this.datasetFileName = file.name;
    this.config.size = file.size;
    this.benchmarkApi.uploadDataset(file).subscribe({
      next: (response: UploadResponse) => {
        if (response.datasetId) {
          this.config.datasetId = response.datasetId;
          console.log('Dataset uploaded successfully:', response);
        }
      },
      error: (error) => {
        console.error('Error uploading dataset:', error);
        this.datasetFileName = '';
      }
    });
  }

  onMultipleDatasetUpload(files: File[]) {
    this.datasetFileName = `${files.length} files selected`;
    this.config.size = files.reduce((total, file) => total + file.size, 0);

    this.benchmarkApi.uploadMultipleDatasets(files).subscribe({
      next: (response: UploadResponse) => {
        if (response.datasetId) {
          this.config.datasetId = response.datasetId;
          this.datasetFileName = `${response.fileCount} files (${response.documentCount} documents)`;
          console.log('Multiple datasets uploaded successfully:', response);
        }
      },
      error: (error) => {
        console.error('Error uploading multiple datasets:', error);
        this.datasetFileName = '';
      }
    });
  }

  onGroundTruthUpload(file: File) {
    this.groundTruthFileName = file.name;
    this.benchmarkApi.uploadGroundTruth(file).subscribe({
      next: (response: UploadResponse) => {
        if (response.groundTruth && response.queries) {
          this.config.groundTruth = response.groundTruth;
          this.config.queries = response.queries;
          console.log('Ground truth and queries uploaded successfully:', response);
        }
      },
      error: (error) => {
        console.error('Error uploading ground truth:', error);
        this.groundTruthFileName = '';
      }
    });
  }

  onStartBenchmark() {
    console.log('Starting benchmark with config:', this.benchmarkResult);
    if (!this.isConfigValid()) {
      return;
    }

    this.isRunning = true;
    this.benchmarkResult = null;

    this.benchmarkApi.startBenchmark(this.config).subscribe({
      next: (response) => {
        this.currentJobId = response.jobId;
        console.log('Benchmark started:', response);
        this.startPolling();
      },
      error: (error) => {
        console.error('Error starting benchmark:', error);
        this.isRunning = false;
      }
    });
  }

  private startPolling() {
    if (!this.currentJobId) return;

    this.pollInterval = setInterval(() => {
      if (this.currentJobId) {
        this.benchmarkApi.getBenchmarkResult(this.currentJobId).subscribe({
          next: (result) => {
            this.benchmarkResult = result;

            if (result.status === BenchmarkStatus.COMPLETED || result.status === BenchmarkStatus.FAILED) {
              this.isRunning = false;
              clearInterval(this.pollInterval);
            }
          },
          error: (error) => {
            console.error('Error polling benchmark result:', error);
            this.isRunning = false;
            clearInterval(this.pollInterval);
          }
        });
      }
    }, 2000); // Poll every 2 seconds
  }

  isConfigValid(): boolean {
    return !!(
      this.config.modelId &&
      this.config.datasetId &&
      this.config.queries.length > 0
    );
  }
}
