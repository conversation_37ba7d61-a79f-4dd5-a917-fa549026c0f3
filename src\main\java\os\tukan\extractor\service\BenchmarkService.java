package os.tukan.extractor.service;

import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import os.tukan.extractor.config.BenchmarkConfig;
import os.tukan.extractor.config.EmbeddingModelConfig;
import os.tukan.extractor.config.GroundTruthDataset;
import os.tukan.extractor.model.*;
import os.tukan.extractor.vectorDB.ElasticService;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Log4j2
@Service
public class BenchmarkService {

    private final ElasticService elasticService;
    private final EmbeddingModelManager embeddingModelManager;
    private final BenchmarkConfig benchmarkConfig;
    private final OllamaModelDiscoveryService ollamaModelDiscoveryService;

    // In-memory storage for benchmark results (in production, use Redis or
    // database)
    private final Map<String, BenchmarkResult> benchmarkResults = new ConcurrentHashMap<>();
    private final Map<String, List<String>> datasetStorage = new ConcurrentHashMap<>();

    public BenchmarkService(ElasticService elasticService,
            EmbeddingModelManager embeddingModelManager,
            BenchmarkConfig benchmarkConfig,
            OllamaModelDiscoveryService ollamaModelDiscoveryService) {
        this.elasticService = elasticService;
        this.embeddingModelManager = embeddingModelManager;
        this.benchmarkConfig = benchmarkConfig;
        this.ollamaModelDiscoveryService = ollamaModelDiscoveryService;
    }

    // Utility methods for common validation and operations
    private void validateNotNull(Object value) {
        if (value == null) {
            throw new IllegalArgumentException("BenchmarkRequest" + " cannot be null");
        }
    }

    private void validateNotEmpty(String value, String fieldName) {
        if (value == null || value.isEmpty()) {
            throw new IllegalArgumentException(fieldName + " cannot be null or empty");
        }
    }

    private void validateNotEmpty(Collection<?> value, String fieldName) {
        if (value == null || value.isEmpty()) {
            throw new IllegalArgumentException(fieldName + " cannot be null or empty");
        }
    }

    private BenchmarkResult createBenchmarkResult(String jobId, BenchmarkStatus status) {
        return new BenchmarkResult(jobId, status);
    }

    private BenchmarkResult createBenchmarkResult(String jobId, String errorMessage) {
        return new BenchmarkResult(jobId, BenchmarkStatus.FAILED, errorMessage);
    }

    private EmbeddingTimeMetrics createEmbeddingTimeMetrics(List<Long> documentTimes, List<Long> queryTimes,
                                                           String modelName, String modelProvider) {
        EmbeddingTimeMetrics timeMetrics = new EmbeddingTimeMetrics();
        timeMetrics.setDocumentEmbeddingTimes(documentTimes);
        timeMetrics.setQueryEmbeddingTimes(queryTimes);
        timeMetrics.setModelName(modelName);
        timeMetrics.setModelProvider(modelProvider);
        timeMetrics.calculateStatistics();
        return timeMetrics;
    }

    private void setQueryMetrics(QueryBenchmarkResult result, double precision, double recall,
                                double f1, double ndcg, double mrr) {
        result.setPrecision(precision);
        result.setRecall(recall);
        result.setF1(f1);
        result.setNdcg(ndcg);
        result.setMrr(mrr);
    }

    private void validateIndexedDocuments() throws Exception {
        long docCount = elasticService.getDocumentCount();
        if (docCount == 0) {
            throw new IllegalStateException("No documents indexed");
        }
    }

    /**
     * Get available embedding models from Ollama discovery service
     */
    public List<EmbeddingModelConfig> getAvailableModels() {
        try {
            return ollamaModelDiscoveryService.getAvailableEmbeddingModels().stream()
                    .filter(embeddingModelManager::testModelConnection)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * Clear model cache to force refresh
     */
    public void clearModelCache() {
        ollamaModelDiscoveryService.clearCache();
        embeddingModelManager.clearCache();
    }


    public String storeDataset(List<String> documents) {
        String datasetId = UUID.randomUUID().toString();
        datasetStorage.put(datasetId, new ArrayList<>(documents));
        return datasetId;
    }


    public List<String> getDataset(String datasetId) {
        return datasetStorage.get(datasetId);
    }

    /**
     * Start asynchronous benchmark execution
     */
    public String startAsyncBenchmark(BenchmarkRequest request) {
        String jobId = UUID.randomUUID().toString();
        request.setJobId(jobId);

        // Initialize result with PENDING status
        BenchmarkResult initialResult = createBenchmarkResult(jobId, BenchmarkStatus.PENDING);
        benchmarkResults.put(jobId, initialResult);

        // Start async execution
        runBenchmarkAsync(request);

        return jobId;
    }

    /**
     * Get benchmark result by job ID
     */
    public BenchmarkResult getBenchmarkResult(String jobId) {
        return benchmarkResults.get(jobId);
    }

    /**
     * Get all completed benchmark results
     */
    public List<BenchmarkResult> getAllCompletedResults() {
        return benchmarkResults.values().stream()
                .filter(result -> result.getStatus() == BenchmarkStatus.COMPLETED)
                .sorted((a, b) -> {
                    // Sort by creation time if available, otherwise by jobId
                    if (a.getCreatedAt() != null && b.getCreatedAt() != null) {
                        return b.getCreatedAt().compareTo(a.getCreatedAt()); // Most recent first
                    }
                    return b.getJobId().compareTo(a.getJobId());
                })
                .collect(Collectors.toList());
    }

    /**
     * Save a benchmark result with a custom label
     */
    public boolean saveBenchmarkResult(String jobId, String label) {
        BenchmarkResult result = benchmarkResults.get(jobId);
        if (result == null || result.getStatus() != BenchmarkStatus.COMPLETED) {
            return false;
        }

        result.setLabel(label);
        if (result.getCreatedAt() == null) {
            result.setCreatedAt(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }

        return true;
    }

    /**
     * Delete a benchmark result
     */
    public boolean deleteBenchmarkResult(String jobId) {
        return benchmarkResults.remove(jobId) != null;
    }

    @Async
    public void runBenchmarkAsync(BenchmarkRequest request) {
        try {
            // Update status to RUNNING
            BenchmarkResult runningResult = createBenchmarkResult(request.getJobId(), BenchmarkStatus.RUNNING);
            benchmarkResults.put(request.getJobId(), runningResult);

            // Execute benchmark
            BenchmarkResult result = executeBenchmark(request);

            // Store final result
            benchmarkResults.put(request.getJobId(), result);

        } catch (Exception e) {
            BenchmarkResult failedResult = createBenchmarkResult(request.getJobId(), e.getMessage());
            benchmarkResults.put(request.getJobId(), failedResult);
        }

        CompletableFuture.completedFuture(null);
    }

    private BenchmarkResult executeBenchmark(BenchmarkRequest request) throws Exception {
        long startTime = System.nanoTime();

        String sessionId = UUID.randomUUID().toString().substring(0, 8);

        // Validate request
        validateBenchmarkRequest(request);

        EmbeddingModelConfig modelConfig = getModelConfig(request.getModelId());

        List<String> dataset = getDataset(request.getDatasetId());
        validateNotEmpty(dataset, "Dataset for ID: " + request.getDatasetId());

        String indexName = ollamaModelDiscoveryService.generateIndexName(modelConfig.getModelName(),
                request.getSimilarityFunction());
        elasticService.setIndexName(indexName);
        elasticService.setSimilarityFunction(request.getSimilarityFunction());

        elasticService.initializeElasticsearchResources();
        EmbeddingTimeMetrics datasetTimeMetrics = indexDataset(dataset, modelConfig, request.getJobId());
        List<QueryBenchmarkResult> queryResults = executeQueries(request, modelConfig);

        // Calculate overall metrics with embedding time data
        BenchmarkMetrics overallMetrics = calculateOverallMetrics(queryResults, dataset.size(), datasetTimeMetrics);

        long executionTime = (System.nanoTime() - startTime) / 1_000_000;

        BenchmarkResult result = new BenchmarkResult();
        result.setJobId(request.getJobId());
        result.setStatus(BenchmarkStatus.COMPLETED);
        result.setOverallMetrics(overallMetrics);
        result.setQueryResults(queryResults);
        result.setExecutionTimeMs(executionTime);
        result.setModelInfo(modelConfig);
        result.setSessionId(sessionId);
        result.setCreatedAt(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        return result;
    }

    private void validateBenchmarkRequest(BenchmarkRequest request) {
        validateNotNull(request);
        validateNotEmpty(request.getModelId(), "Model ID");
        validateNotEmpty(request.getDatasetId(), "Dataset ID");
        validateNotEmpty(request.getQueries(), "Queries");

        if (request.getTopK() <= 0 || request.getTopK() > benchmarkConfig.getMaxTopK()) {
            throw new IllegalArgumentException("TopK must be between 1 and " + benchmarkConfig.getMaxTopK());
        }
    }

    private EmbeddingModelConfig getModelConfig(String modelId) {
        try {
            return ollamaModelDiscoveryService.getAvailableEmbeddingModels().stream()
                    .filter(model -> model.getId().equals(modelId))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("Model not found: " + modelId));
        } catch (Exception e) {
            throw new IllegalArgumentException("Model not found or Ollama service unavailable: " + modelId);
        }
    }

    private EmbeddingTimeMetrics indexDataset(List<String> dataset, EmbeddingModelConfig modelConfig, String jobId) throws Exception {


        List<Long> documentEmbeddingTimes = new ArrayList<>();

        for (int i = 0; i < dataset.size(); i++) {
            String originalDocument = dataset.get(i);
            String normalizedDocId = extractDocumentId(originalDocument);
            String cleanedText = cleanDocumentText(originalDocument);

            // Generate embedding with timing
            long start = System.nanoTime();
            EmbeddingResult embeddingResult = embeddingModelManager.generateEmbeddingWithTiming(cleanedText, modelConfig);
            long durationMs = (System.nanoTime() - start) / 1_000_000;
            documentEmbeddingTimes.add(durationMs);

            Map<String, Object> metadata = Map.of("job_id", jobId, "doc_index", i);
            elasticService.indexDocument(normalizedDocId, cleanedText, metadata, embeddingResult.getEmbedding());
        }

        // Refresh index and validate
        elasticService.refreshIndex();
        validateIndexedDocuments();

        // Create embedding time metrics for dataset
        return createEmbeddingTimeMetrics(documentEmbeddingTimes, null,
                                        modelConfig.getModelName(), modelConfig.getProvider());
    }

    private String normalizeDocumentId(String docId) {
        if (docId == null)
            return "";
        return docId.replaceAll("(?i)\\.pdf$", "").replaceAll("[^\\p{Alnum}]", "").toLowerCase(Locale.ROOT);
    }

    private String cleanDocumentText(String text) {
        if (text == null || text.isEmpty())
            return text;
        if (text.startsWith("[") && text.contains("] ")) {
            int endBracket = text.indexOf("] ");
            if (endBracket > 0)
                return text.substring(endBracket + 2).trim();
        }
        return text.trim();
    }

    private String extractDocumentId(String document) {
        if (document.startsWith("[") && document.contains("]")) {
            int endBracket = document.indexOf("]");
            if (endBracket > 1) {
                String rawId = document.substring(1, endBracket).trim();
                if (rawId.contains("/")) {
                    rawId = rawId.substring(rawId.lastIndexOf("/") + 1);
                }
                return normalizeDocumentId(rawId);
            }
        }
        return normalizeDocumentId("Document");
    }

    private List<QueryBenchmarkResult> executeQueries(BenchmarkRequest request, EmbeddingModelConfig modelConfig)
            throws Exception {
        List<QueryBenchmarkResult> results = new ArrayList<>();

        for (int queryIndex = 0; queryIndex < request.getQueries().size(); queryIndex++) {
            String query = request.getQueries().get(queryIndex);
            long queryStart = System.nanoTime();

            // Generate embedding with timing
            EmbeddingResult embeddingResult = embeddingModelManager.generateEmbeddingWithTiming(query, modelConfig);
            float[] queryEmbedding = embeddingResult.getEmbedding();
            long queryEmbeddingTime = embeddingResult.getEmbeddingTimeMs();

            List<Map<String, Object>> searchResults = elasticService.knnSearch(queryEmbedding, request.getTopK());
            long queryTime = (System.nanoTime() - queryStart) / 1_000_000;

            List<String> retrievedDocIds = searchResults.stream()
                    .map(result -> result.get("id").toString())
                    .collect(Collectors.toList());

            List<Double> similarityScores = searchResults.stream()
                    .map(result -> (Double) result.get("score"))
                    .collect(Collectors.toList());

            Map<String, Integer> gradedRelevance = GroundTruthDataset.getGradedRelevance(query);
            List<String> relevantDocIds;

            if (!gradedRelevance.isEmpty()) {
                relevantDocIds = gradedRelevance.entrySet().stream()
                        .filter(entry -> entry.getValue() > 0)
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
            } else if (request.getGroundTruth() != null) {
                relevantDocIds = request.getGroundTruth().getOrDefault(query, Collections.emptyList());
                // Create graded relevance from simple ground truth based on position
                gradedRelevance = createGradedRelevanceFromSimpleGroundTruth(relevantDocIds);
            } else {
                relevantDocIds = Collections.emptyList();
            }

            QueryBenchmarkResult queryResult = calculateQueryMetricsWithGradedRelevance(
                    query, retrievedDocIds, relevantDocIds, gradedRelevance, queryTime, queryEmbeddingTime, similarityScores);
            results.add(queryResult);
        }

        return results;
    }

    private QueryBenchmarkResult calculateQueryMetricsWithGradedRelevance(String query, List<String> retrievedDocIds,
            List<String> relevantDocIds, Map<String, Integer> gradedRelevance, long queryTime, long queryEmbeddingTime,
            List<Double> similarityScores) {
        QueryBenchmarkResult result = new QueryBenchmarkResult(query, retrievedDocIds, relevantDocIds);
        result.setQueryTimeMs(queryTime);
        result.setQueryEmbeddingTimeMs(queryEmbeddingTime);
        result.setSimilarityScores(similarityScores);

        if (relevantDocIds.isEmpty()) {
            setQueryMetrics(result, 0.0, 0.0, 0.0, 0.0, 0.0);
            return result;
        }

        // Normalize all document IDs for consistent comparison
        Set<String> relevantSet = new HashSet<>();
        for (String docId : relevantDocIds) {
            relevantSet.add(normalizeDocumentId(docId));
        }

        int truePositives = (int) retrievedDocIds.stream()
                .mapToLong(id -> relevantSet.contains(normalizeDocumentId(id)) ? 1 : 0)
                .sum();

        double precision = retrievedDocIds.isEmpty() ? 0.0 : (double) truePositives / retrievedDocIds.size();
        double recall = (double) truePositives / relevantDocIds.size();
        double f1 = (precision + recall == 0) ? 0.0 : 2 * precision * recall / (precision + recall);

        double mrr = 0.0;
        for (int i = 0; i < retrievedDocIds.size(); i++) {
            if (relevantSet.contains(normalizeDocumentId(retrievedDocIds.get(i)))) {
                mrr = 1.0 / (i + 1);
                break;
            }
        }

        double ndcg = calculateNDCGWithGradedRelevance(retrievedDocIds, gradedRelevance, relevantDocIds);

        setQueryMetrics(result, precision, recall, f1, ndcg, mrr);

        return result;
    }

    private double calculateNDCGWithGradedRelevance(List<String> retrievedDocIds,
            Map<String, Integer> gradedRelevance, List<String> relevantDocIds) {
        if (retrievedDocIds.isEmpty())
            return 0.0;

        // If no graded relevance available, create it from relevant docs
        if (gradedRelevance.isEmpty() && !relevantDocIds.isEmpty()) {
            gradedRelevance = createGradedRelevanceFromSimpleGroundTruth(relevantDocIds);
        }

        if (gradedRelevance.isEmpty())
            return 0.0;

        double dcg = 0.0;
        for (int i = 0; i < retrievedDocIds.size(); i++) {
            String docId = retrievedDocIds.get(i);
            Integer relevanceScore = gradedRelevance.get(docId);
            if (relevanceScore != null && relevanceScore > 0) {
                double gain = Math.pow(2, relevanceScore) - 1;
                double discount = Math.log(i + 2) / Math.log(2);
                dcg += gain / discount;
            }
        }

        List<Integer> idealRelevanceScores = gradedRelevance.values().stream()
                .filter(score -> score > 0)
                .sorted(Collections.reverseOrder())
                .toList();

        double idcg = 0.0;
        for (int i = 0; i < Math.min(idealRelevanceScores.size(), retrievedDocIds.size()); i++) {
            int relevanceScore = idealRelevanceScores.get(i);
            double gain = Math.pow(2, relevanceScore) - 1;
            double discount = Math.log(i + 2) / Math.log(2);
            idcg += gain / discount;
        }

        return idcg == 0 ? 0.0 : dcg / idcg;
    }

    private BenchmarkMetrics calculateOverallMetrics(List<QueryBenchmarkResult> queryResults, int datasetSize,
                                                    EmbeddingTimeMetrics datasetTimeMetrics) {
        if (queryResults.isEmpty())
            return BenchmarkMetrics.empty();

        double avgPrecision = queryResults.stream().mapToDouble(QueryBenchmarkResult::getPrecision).average()
                .orElse(0.0);
        double avgRecall = queryResults.stream().mapToDouble(QueryBenchmarkResult::getRecall).average().orElse(0.0);
        double avgF1 = queryResults.stream().mapToDouble(QueryBenchmarkResult::getF1).average().orElse(0.0);
        double avgNdcg = queryResults.stream().mapToDouble(QueryBenchmarkResult::getNdcg).average().orElse(0.0);
        double avgMrr = queryResults.stream().mapToDouble(QueryBenchmarkResult::getMrr).average().orElse(0.0);
        double avgQueryTime = queryResults.stream().mapToDouble(QueryBenchmarkResult::getQueryTimeMs).average()
                .orElse(0.0);

        // Collect query embedding times
        List<Long> queryEmbeddingTimes = queryResults.stream()
                .map(QueryBenchmarkResult::getQueryEmbeddingTimeMs)
                .collect(Collectors.toList());

        // Create complete embedding time metrics
        EmbeddingTimeMetrics completeTimeMetrics = createEmbeddingTimeMetrics(
                datasetTimeMetrics.getDocumentEmbeddingTimes(), queryEmbeddingTimes,
                datasetTimeMetrics.getModelName(), datasetTimeMetrics.getModelProvider());

        BenchmarkMetrics metrics = new BenchmarkMetrics(avgPrecision, avgRecall, avgF1, avgNdcg, avgMrr, avgQueryTime,
                queryResults.size(), datasetSize);
        metrics.setEmbeddingTimeMetrics(completeTimeMetrics);

        return metrics;
    }

    // ============================================================================
    // UTILITY METHODS
    // ============================================================================

    /**
     * Create graded relevance scores from simple ground truth based on document position.
     * First document gets highest score (5), subsequent documents get decreasing scores.
     * This ensures proper nDCG calculation even with simple ground truth format.
     */
    private Map<String, Integer> createGradedRelevanceFromSimpleGroundTruth(List<String> relevantDocIds) {
        Map<String, Integer> gradedRelevance = new HashMap<>();

        for (int i = 0; i < relevantDocIds.size(); i++) {
            String docId = relevantDocIds.get(i);
            // Assign scores: 5 for first doc, 4 for second, etc., minimum score of 1
            int score = Math.max(1, 5 - i);
            gradedRelevance.put(normalizeDocumentId(docId), score);
        }

        return gradedRelevance;
    }


}