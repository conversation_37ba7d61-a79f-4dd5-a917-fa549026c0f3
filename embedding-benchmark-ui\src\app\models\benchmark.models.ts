export interface EmbeddingModelConfig {
  id: string;
  name: string;
  provider: string;
  endpoint?: string;
  modelName: string;
  parameters?: { [key: string]: any };
  // Additional metadata from Ollama
  size?: number;
  modifiedAt?: string;
  family?: string;
  parameterSize?: string;
}

export interface BenchmarkRequest {
  modelId: string;
  datasetId: string;
  queries: string[];
  groundTruth: { [query: string]: string[] };
  similarityFunction: string;
  topK: number;
  jobId?: string;
}

export interface EmbeddingTimeMetrics {
  totalDatasetEmbeddingTimeMs: number;
  avgDocumentEmbeddingTimeMs: number;
  totalDocuments: number;
  documentEmbeddingTimes: number[];

  totalQueryEmbeddingTimeMs: number;
  avgQueryEmbeddingTimeMs: number;
  totalQueries: number;
  queryEmbeddingTimes: number[];

  modelName: string;
  modelProvider: string;

  minDocumentEmbeddingTimeMs: number;
  maxDocumentEmbeddingTimeMs: number;
  minQueryEmbeddingTimeMs: number;
  maxQueryEmbeddingTimeMs: number;
}

export interface BenchmarkMetrics {
  avgPrecision: number;
  avgRecall: number;
  avgF1: number;
  avgNdcg: number;
  avgMrr: number;
  avgQueryTimeMs: number;
  totalQueries: number;
  totalDocuments: number;
  embeddingTimeMetrics?: EmbeddingTimeMetrics;
}

export interface QueryBenchmarkResult {
  query: string;
  retrievedDocIds: string[];
  relevantDocIds: string[];
  precision: number;
  recall: number;
  f1: number;
  ndcg: number;
  mrr: number;
  queryTimeMs: number;
  queryEmbeddingTimeMs: number;
}

export enum BenchmarkStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export interface BenchmarkResult {
  jobId: string;
  status: BenchmarkStatus;
  overallMetrics?: BenchmarkMetrics;
  queryResults?: QueryBenchmarkResult[];
  executionTimeMs?: number;
  modelInfo?: EmbeddingModelConfig;
  errorMessage?: string;
  sessionId?: string;
  label?: string;
  createdAt?: string;
}

export interface SavedBenchmarkResult extends BenchmarkResult {
  label: string;
  createdAt: string;
}

export interface ComparisonMetrics {
  precisionDiff: number;
  recallDiff: number;
  f1Diff: number;
  ndcgDiff: number;
  mrrDiff: number;
  queryTimeDiff: number;
  embeddingTimeDiff?: number;
}

export interface ComparisonResult {
  primary: BenchmarkResult;
  secondary: BenchmarkResult;
  overallComparison: ComparisonMetrics;
  queryComparisons: QueryComparisonResult[];
}

export interface QueryComparisonResult {
  query: string;
  primary: QueryBenchmarkResult;
  secondary: QueryBenchmarkResult;
  metrics: ComparisonMetrics;
  rankingChanges: RankingChange[];
}

export interface RankingChange {
  documentId: string;
  primaryRank: number;
  secondaryRank: number;
  rankDifference: number;
}

export interface UploadResponse {
  datasetId?: string;
  queries?: string[];
  groundTruth?: { [query: string]: string[] };
  documentCount?: number;
  queryCount?: number;
  fileCount?: number;
  size?: number;
  message: string;
  error?: string;
}

export interface BenchmarkConfig {
  modelId: string;
  datasetId: string;
  queries: string[];
  size?: number;
  groundTruth: { [query: string]: string[] };
  similarityFunction: string;
  topK: number;
}
