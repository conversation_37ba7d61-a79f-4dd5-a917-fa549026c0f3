<div class="card">
  <div class="flex items-center justify-between mb-4">
    <label class="block text-sm font-medium text-gray-700 flex items-center">
      <i class="fas fa-brain mr-2 text-blue-500"></i>
      Embedding Model
    </label>
    <button (click)="refreshModels()" [disabled]="loading || refreshing" class="btn-secondary"
      title="Refresh models from Ollama">
      <i class="fas fa-sync-alt mr-1" [class.fa-spin]="refreshing"></i>
      Refresh
    </button>
  </div>

  <div *ngIf="loading" class="flex items-center justify-center py-4">
    <div class="simple-spinner" aria-label="Loading"></div>
    <span class="ml-2 text-gray-600">Loading models from Ollama...</span>
  </div>

  <div *ngIf="refreshing" class="flex items-center justify-center py-2">
    <div class="loading-spinner"></div>
    <span class="ml-2 text-gray-600">Refreshing models...</span>
  </div>

  <div *ngIf="error" class="text-red-600 text-sm mb-2 p-2 bg-red-50 rounded border border-red-200">
    <i class="fas fa-exclamation-triangle mr-1"></i>
    {{ error }}
  </div>

  <div class="relative">
    <select [(ngModel)]="selectedModel" (ngModelChange)="onModelChange($event)" [disabled]="loading || refreshing"
      class="form-select">
      <option value="" disabled selected>Select an embedding model...</option>
      <option *ngFor="let model of models" [value]="model.id">
        {{ model.name }}
      </option>
    </select>
  </div>

  <div *ngIf="selectedModelInfo" class="mt-3 p-3 bg-blue-50 rounded-md border border-blue-200">
    <h4 class="text-sm font-medium text-blue-900 mb-2">
      <i class="fas fa-info-circle mr-1"></i>
      Model Information
    </h4>
    <div class="text-sm text-blue-700 space-y-1">
      <div><strong>Name:</strong> {{ selectedModelInfo.name }}</div>
      <div><strong>Provider:</strong> {{ selectedModelInfo.provider }}</div>
      <div><strong>Model ID:</strong> {{ selectedModelInfo.modelName }}</div>
      <div *ngIf="selectedModelInfo.size">
        <strong>Size:</strong> {{ formatModelSize(selectedModelInfo.size) }}
      </div>
      <div *ngIf="selectedModelInfo.family">
        <strong>Family:</strong> {{ selectedModelInfo.family }}
      </div>
      <div *ngIf="selectedModelInfo.parameterSize">
        <strong>Parameters:</strong> {{ selectedModelInfo.parameterSize }}
      </div>
      <div *ngIf="selectedModelInfo.modifiedAt">
        <strong>Modified:</strong> {{ formatModifiedDate(selectedModelInfo.modifiedAt) }}
      </div>
      <div *ngIf="selectedModelInfo.endpoint">
        <strong>Endpoint:</strong> {{ selectedModelInfo.endpoint }}
      </div>
    </div>
  </div>

  <div *ngIf="models.length === 0 && !loading && !error" class="text-gray-500 text-sm mt-2 p-2 bg-gray-50 rounded">
    <i class="fas fa-info-circle mr-1"></i>
    No embedding models found. Please ensure Ollama is running and has embedding models installed.
  </div>
</div>