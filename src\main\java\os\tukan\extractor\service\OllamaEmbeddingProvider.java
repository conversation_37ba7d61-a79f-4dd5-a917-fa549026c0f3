package os.tukan.extractor.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import os.tukan.extractor.config.EmbeddingModelConfig;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;

@Log4j2
@Component
public class OllamaEmbeddingProvider implements EmbeddingProvider {

    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;
    private EmbeddingModelConfig config;

    public OllamaEmbeddingProvider() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    public void setConfig(EmbeddingModelConfig config) {
        this.config = config;
    }

    @Override
    public float[] embed(String text) throws Exception {
        if (config == null) {
            throw new IllegalStateException("Embedding model config not set");
        }

        String endpoint = config.getEndpoint();
        if (endpoint == null || endpoint.isEmpty()) {
            endpoint = "http://10.10.104.127:11434/api/embed";
        }

        Map<String, Object> requestBody = Map.of(
                "model", config.getModelName(),
                "input", text);

        String jsonBody = objectMapper.writeValueAsString(requestBody);

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(endpoint))
                .timeout(Duration.ofSeconds(30))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(jsonBody, StandardCharsets.UTF_8))
                .build();

        try {
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                throw new Exception(
                        "Ollama API returned status: " + response.statusCode() + ", body: " + response.body());
            }

            JsonNode jsonResponse = objectMapper.readTree(response.body());
            JsonNode embeddingsNode = jsonResponse.get("embeddings");

            if (embeddingsNode == null || !embeddingsNode.isArray() || embeddingsNode.size() == 0) {
                throw new Exception("Invalid response format from Ollama API");
            }

            JsonNode embeddingArray = embeddingsNode.get(0);
            float[] embedding = new float[embeddingArray.size()];

            for (int i = 0; i < embeddingArray.size(); i++) {
                embedding[i] = (float) embeddingArray.get(i).asDouble();
            }

            return embedding;

        } catch (Exception e) {
            throw new Exception("Ollama embedding generation failed: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            if (config == null) {
                return false;
            }

            // Test with a simple text
            float[] testEmbedding = embed("test");
            return testEmbedding != null && testEmbedding.length > 0;
        } catch (Exception e) {
            return false;
        }
    }

}
