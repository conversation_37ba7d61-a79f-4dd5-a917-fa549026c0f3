<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-8 flex items-center">
      <i class="fas fa-chart-line mr-3"></i>
      Embedding Model Benchmark Tool
    </h1>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Configuration Panel -->
      <div class="space-y-6 card-panel">
        <h2 class="panel-title"><i class="fas fa-cogs"></i> Configuration</h2>
        <app-model-selector
          [selectedModel]="config.modelId"
          (modelChange)="onModelChange($event)">
        </app-model-selector>
        
        <app-file-uploader
          title="Dataset"
          description="Upload PDF, TXT, or JSON dataset files (supports multiple files and directories)"
          [acceptedTypes]="datasetAcceptedTypes"
          [uploadedFileName]="datasetFileName"
          [allowMultiple]="true"
          [allowDirectory]="true"
          (fileUpload)="onDatasetUpload($event)"
          (multipleFileUpload)="onMultipleDatasetUpload($event)">
        </app-file-uploader>
        
        <app-file-uploader
          title="Queries & Ground Truth"
          description="Upload JSON file with queries and ground truth mappings (supports both combined dataset format and simple ground truth format)"
          [acceptedTypes]="groundTruthAcceptedTypes"
          [uploadedFileName]="groundTruthFileName"
          (fileUpload)="onGroundTruthUpload($event)">
        </app-file-uploader>
        
        <app-benchmark-runner
          [config]="config"
          [isRunning]="isRunning"
          [isConfigValid]="isConfigValid()"
          (startBenchmark)="onStartBenchmark()">
        </app-benchmark-runner>
      </div>
      
      <!-- Results Panel -->
      <div class="card-panel">
        <h2 class="panel-title"><i class="fas fa-poll"></i> Results</h2>
        <app-results-display
          [result]="benchmarkResult"
          [isLoading]="isRunning">
        </app-results-display>
      </div>
    </div>
  </div>
</div>
